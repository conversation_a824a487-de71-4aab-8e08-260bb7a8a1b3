#include "command_util.h"



void CloseProcess(int sig)
{
    char logMsg[512];
    sprintf(logMsg,"Start & Exit [%d][%d]", getpid(), sig);
    monitoring(logMsg,0,0);
    activeProcess = FALSE;
}

void Init_Server_Fork()
{
    signal( SIGHUP,  CloseProcess );
    signal( SIGCLD,  sig_rtn );
    signal( <PERSON><PERSON><PERSON><PERSON><PERSON>, SIG_IGN );
    signal( SIGTERM, CloseProcess );
    signal( SIGINT,  CloseProcess );
    signal( SIGQUIT, CloseProcess );
    signal( SIGKILL, CloseProcess );
    signal( SIGSTOP, CloseProcess );
    signal( SIGUSR1, CloseProcess );
    signal( SIGCHLD, sig_rtn );   
}

void sig_rtn(int sig)
{
    pid_t childpid;
    do {
        childpid = waitpid( (pid_t) -1, NULL , WNOHANG);
        if( childpid == (pid_t)-1)
            break;
    } while(childpid != (pid_t) 0);
    signal( SIGCHLD, sig_rtn );   
}




void monitoring(const char* format,...)
{
    va_list args;
    char logMsg[MAXSIZE];

    va_start(args, format);
    vsprintf(logMsg, format, args);
    va_end(args);


    if (ml_sub_send_moni(logMsg, strlen(logMsg), 3, 0, 0) <= 0) {
        printf("%s ml_sub_send_moni error. %d %s\n", PROCESS_NAME, errno, strerror(errno));
    }   
}

void logmessage(const char* format,...)
{
    va_list args;
    char logMsg[MAXSIZE];

    va_start(args, format);
    vsprintf(logMsg, format, args);
    va_end(args);

    if (ml_sub_send_log(logMsg, strlen(logMsg), 3, 0, 0) <= 0) {
        printf("%s ml_sub_send_log error. %d %s\n", PROCESS_NAME, errno, strerror(errno));
    }   
}



