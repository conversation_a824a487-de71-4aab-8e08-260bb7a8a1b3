#include "mappingfile.h"
#include "command_util.h"

#include <cstdlib>  // strtol을 사용하기 위해 필요
#include <cerrno>   // errno를 사용하기 위해 필요
#include <climits>  // LONG_MAX, LONG_MIN을 사용하기 위해 필요

CMappingFile::CMappingFile()
{
    fp = NULL;

    memset(szDstAddr,0x00,sizeof(szDstAddr));
    memset(szCallBack,0x00,sizeof(szCallBack));
    memset(szMsg,0x00,sizeof(szMsg));
    memset(szResv,0x00,sizeof(szResv));
    memset(szCharge,0x00,sizeof(szCharge));
    memset(szChannel,0x00,sizeof(szChannel)); 
    memset(szTmplCd,0x00,sizeof(szTmplCd));
    memset(szMbaseId,0x00,sizeof(szMbaseId));
    memset(szAtalkMsg,0x00,sizeof(szAtalkMsg));
    memset(szRcsMsg,0x00,sizeof(szRcsMsg));
    memset(szSendKey,0x00,sizeof(szSendKey));
    memset(szButton,0x00,sizeof(szButton));
    memset(szTitle,0x00,sizeof(szTitle));
    memset(szReplaceMsg,0x00,sizeof(szReplaceMsg));
    memset(szFallbackMbaseId, 0x00, sizeof(szFallbackMbaseId));
    memset(szFallbackRcsMsg, 0x00, sizeof(szFallbackRcsMsg));    
}

CMappingFile::~CMappingFile()
{
    this->close();
}

int CMappingFile::open(char* filename,int nWorkCnt,int maxCount)
{
    fp = fopen(filename,"r");
    if( fp == NULL )
        return -1;

    fseek(fp,0L,SEEK_SET);

    int nCnt=0;
    //char buff[1024];
    //char buff[3072];
    //char buff[5072];
    //char buff[5172];
    char buff[10240];

    memset(buff,0x00,sizeof(buff));

    while( nCnt < nWorkCnt )
    {
        //if( fgets(buff,1024,fp) == NULL )
        //if( fgets(buff,3072,fp) == NULL )
        //if( fgets(buff,5072,fp) == NULL )
        //if( fgets(buff,5172,fp) == NULL )
       	if( fgets(buff,10240,fp) == NULL )
            break;
        nCnt++;
    }

    if( nCnt != nWorkCnt )
    {
        monitoring("nCnt [%d] nWorkCnt [%d]\n",nCnt,nWorkCnt);
        return -1;
    }

    maxMappingCount = maxCount;

    return 0;
}

int CMappingFile::close()
{
    if( fp != NULL)
        fclose(fp);
    fp=NULL;
    
    memset(szDstAddr,0x00,sizeof(szDstAddr));
    memset(szCallBack,0x00,sizeof(szCallBack));
    memset(szMsg,0x00,sizeof(szMsg));
    memset(szResv,0x00,sizeof(szResv));
    memset(szCharge,0x00,sizeof(szCharge));
    memset(szChannel,0x00,sizeof(szChannel)); 
    memset(szTmplCd,0x00,sizeof(szTmplCd));
    memset(szMbaseId,0x00,sizeof(szMbaseId));
    memset(szAtalkMsg,0x00,sizeof(szAtalkMsg));
    memset(szRcsMsg,0x00,sizeof(szRcsMsg));
    memset(szSendKey,0x00,sizeof(szSendKey));
    memset(szButton,0x00,sizeof(szButton));
    memset(szTitle,0x00,sizeof(szTitle));
    memset(szReplaceMsg,0x00,sizeof(szReplaceMsg));
    memset(szFallbackMbaseId, 0x00, sizeof(szFallbackMbaseId));
    memset(szFallbackRcsMsg, 0x00, sizeof(szFallbackRcsMsg));
    
    return 0;
}

int CMappingFile::getData(
        char* dstInd, 
        char* callY , char* callInd ,
        char* resvY , char* resvInd ,
        std::string msgTemplate,
        std::string separators,
        int f_grp_id)
{
    int nCnt=0;
    char buff[10240];
    char* entry;
//    printf("getData start\n");
    std::string tokenString;

	// RESV_DATA default : 3
	int nResvInd = 0;

	//nResvInd = atoi(resvInd);
		
	if (resvInd != NULL) {
		char* endPtr;
		errno = 0;	// errno 초기화
		long temp = strtol(resvInd, &endPtr, 10);	// 10진수로 변환

		if (errno == ERANGE || temp > INT_MAX || temp < INT_MIN) {
			// 오버플로우 또는 언더플로우 발생 시 처리
            // 예: 기본값 설정 또는 오류 메시지 반환
            logmessage(" resvInd range over error [%lld]", temp);
			nResvInd = 3;
		}
		else if (endPtr == resvInd) {
			// 변환된 숫자가 없을 경우 처리
            logmessage(" resvInd trans error [%lld]", temp);
			nResvInd = 3;
		}
		else  {
			nResvInd = static_cast<int>(temp);
		}
	}
	else {
		// resvInd가 NULL인 경우 처리
        logmessage(" resvInd NULL error [%p]", resvInd);
		nResvInd = 3;
	}

	logmessage("[SHS]mappingfile nResvInd [%d]",nResvInd);

	//logmessage("[SHS]mappingfile getData start0[%d]",f_grp_id);
	
    memset(buff,0x00,sizeof(buff));

    // read 1 line
	if( fgets(buff,10240,fp) == NULL )
        return 0;
        
	// remove return 
    if( buff[strlen(buff) -1] == '\n' )
		buff[strlen(buff)-1] = 0x00;
	
    if( buff[strlen(buff) -1] == '\r' )
		buff[strlen(buff)-1] = 0x00;

    //  token push in list
    std::vector <std::string> entryVector;
    tokenString = buff;
 //   printf("[%s]sep",separators.c_str());
    
    split(tokenString,separators,entryVector);

    for( nCnt=0;nCnt < entryVector.size() ; nCnt++)
    {
      //logmessage("[%s]\n",entryVector.at(nCnt).c_str());
	  logmessage("[%s]\n",entryVector.at(nCnt).c_str());
    }
    nCnt = entryVector.size();

/*
    entry = strtok(buff,token);
    while( entry != NULL )
    {
        nCnt++;
	printf("[%s] [%s]\n",entry,token);
        entryVector.push_back(entry);
        entry = strtok(NULL,token);
    }

*/

//    printf("2getData start entry [%d]\n",nCnt);

	logmessage("[SHS]mappingfile getData start1[%d]",f_grp_id);
    
    // dstaddr
    if( ( atoi(dstInd) < 0 ) || ( atoi(dstInd) >= nCnt ) )
        return -1;

    logmessage("%s:%d\n",__func__,__LINE__);
    memset(szDstAddr,0x00,sizeof(szDstAddr));

    snprintf(szDstAddr, sizeof(szDstAddr), "%s", 
            entryVector.at(atoi(dstInd)).c_str());
    logmessage("%s:%d\n",__func__,__LINE__);

    // callback
    //        if( ( atoi(callInd) < 0 ) || ( atoi(callInd) >= nCnt ) )
    //               return -1;

 //   printf("3getData start dstaddr[%s]\n",szDstAddr);
    memset(szCallBack,0x00,sizeof(szCallBack));
    if( callY[0] == 'Y' || callY[0] =='y' )
    {
        if( ( atoi(callInd) < 0 ) || ( atoi(callInd) >= nCnt ) )
            return -1;
		
        snprintf(szCallBack, sizeof(szCallBack), "%s", 
                entryVector.at(atoi(callInd)).c_str());

    } else {
        //memcpy(szCallBack,callInd, sizeof(szCallBack)-1);
		snprintf(szCallBack, sizeof(szCallBack), "%s", callInd);
    }

    // resv
    //        if( ( atoi(resvInd) < 0 ) || ( atoi(resvInd) >= nCnt ) )
    //                return -1;

//    printf("4getData start\n");
    memset(szResv,0x00,sizeof(szResv));
    if( resvY[0] == 'Y' || resvY[0] =='y' ) {
        //if( ( atoi(resvInd) < 0 ) || ( atoi(resvInd) >= nCnt ) )
    	if( ( nResvInd < 0 ) || ( nResvInd >= nCnt ) )	   	
            return -1;
        
	    //snprintf(szResv, sizeof(szResv), entryVector.at(atoi(resvInd)).c_str() );

		snprintf(szResv, sizeof(szResv),  entryVector.at(nResvInd).c_str() );				
    } 
    else {
		//memcpy(szResv,resvInd, sizeof(szResv)-1);
		snprintf(szResv, sizeof(szResv), "%s", resvInd);
    }
    
   //logmessage("[SHS]mappingfile getData start2[%d]",f_grp_id);

	//if( ( atoi(resvInd)+1 < 0 ) || ( atoi(resvInd)+1 >= nCnt ) )
    //    return -1;

	//유무료구분 1 : 유료 2~3 : 무료 null : 무료
    memset(szCharge,0x00,sizeof(szCharge));
    if (nCnt > 5)
    {
		//snprintf(szCharge, sizeof(szCharge), "%s",
    	//    entryVector.at(atoi(resvInd)+1).c_str()   );

		snprintf(szCharge, sizeof(szCharge), "%s",  entryVector.at(nResvInd+1).c_str()  );
    }             
        
    //if( ( atoi(resvInd)+2 < 0 ) || ( atoi(resvInd)+2 >= nCnt ) )
    //    return -1;

	//채널구분  1~5 : 통신사 SMS 6 : 카카오 알림톡(단문) 7: 카카오 알림톡(장문) 8 : 위비톡 (우리카드) 그외 : SMS
    //		11: NaverTalk
    memset(szChannel,0x00,sizeof(szChannel));          
    if (nCnt > 5)
    {
    	//snprintf(szChannel, sizeof(szChannel), "%s", entryVector.at(atoi(resvInd)+2).c_str() );

		snprintf(szChannel, sizeof(szChannel), "%s", entryVector.at(nResvInd+2).c_str() );
    } 

	int nChannel = 0;
	nChannel = atoi(szChannel);
               
    //템플릿코드
    //if( ( atoi(resvInd)+3 < 0 ) || ( atoi(resvInd)+3 >= nCnt ) )
    //    return -1;
    //20210408 위치변경
    memset(szTmplCd,0x00,sizeof(szTmplCd));
    
    if (nCnt > 8)
    {
		// 11 : NaverTalk
    	//if(atoi(szChannel) == 6 || atoi(szChannel) == 7 || atoi(szChannel) == 9)
    	if (nChannel == 6 || nChannel == 7 || nChannel == 9 || 
			nChannel == 11)
    	{
			//snprintf(szTmplCd, sizeof(szTmplCd), "%s", entryVector.at(atoi(resvInd)+3).c_str() );
			snprintf(szTmplCd, sizeof(szTmplCd), "%s", entryVector.at(nResvInd+3).c_str() );    		
		}
	}
	
	//20210408 위치변경
	memset(szMbaseId,0x00,sizeof(szMbaseId));
	//RCS message base id
	//if(atoi(szChannel) >= 0 && atoi(szChannel) <= 5)
	if(nChannel >= 0 && nChannel <= 5)
   	{
		if(strcmp(szCallBack,"15884000") == 0 || strcmp(szCallBack,"15889955") == 0)
		{
			//snprintf(szMbaseId, sizeof(szMbaseId), "%s",
    		//    entryVector.at(atoi(resvInd)+3).c_str() );    			
			snprintf(szMbaseId, sizeof(szMbaseId), "%s",
    		    entryVector.at(nResvInd+3).c_str() );			
    	}
    }
 //   printf("mapping  start\n");
        // msg mapping
    char szFind[8];
    int idxMapping;
    int n;
    
    std::string talkMsgTemplate;
            
    //대체문구
    //if( ( atoi(resvInd)+4 < 0 ) || ( atoi(resvInd)+4 >= nCnt ) )
    //    return -1;
    
    //logmessage("[SHS]mappingfile getData start3[%d]",f_grp_id);
    //logmessage("[SHS]mappingfile getData start0-1[%d]",f_grp_id);
    
    //20210408 위치변경
    memset(szAtalkMsg,0x00,sizeof(szAtalkMsg));
    
    if (nCnt > 8)
    {
    	if(nChannel == 6 || nChannel == 7 || nChannel == 9 ||
			nChannel == 11)
    	{            
    		//talkMsgTemplate = entryVector.at(atoi(resvInd) + 4).c_str();
			talkMsgTemplate = entryVector.at(nResvInd + 4).c_str();
    		            
    		memset(szFind,0x00,sizeof(szFind));
    		strcpy(szFind,"<BR/>");
			while( (idxMapping = talkMsgTemplate.find(szFind) ) >= 0 ) {
    		    talkMsgTemplate.replace( idxMapping, strlen(szFind), "\n" );
			}
    		               
    		//memcpy(szReplaceMsg,talkMsgTemplate.c_str(),sizeof(szReplaceMsg)-1);                        
    		//memcpy(szAtalkMsg,talkMsgTemplate.c_str(),sizeof(szAtalkMsg)-1);
			snprintf(szAtalkMsg, sizeof(szAtalkMsg), "%s", talkMsgTemplate.c_str() );
    		
    		//printf("szAtalkMsg index[%d]\n",atoi(resvInd)+4);
    		//printf("szAtalkMsg[%s]\n",szAtalkMsg); 
    	}
   }
   
   //logmessage("[SHS]mappingfile getData start0-2[%d]",f_grp_id);
   
   //20210408 위치변경
   memset(szRcsMsg,0x00,sizeof(szRcsMsg));
   
   //RCS SEND MSG
   	if(nChannel >= 0 && nChannel <= 5)
   	{
   	    //	logmessage("[SHS]mappingfile getData start1-2[%d]",f_grp_id);
   	    
		if((strcmp(szCallBack,"15884000") == 0 || strcmp(szCallBack,"15889955") == 0) && strlen(szMbaseId) > 0)
		{
			logmessage("[SHS]mappingfile getData start2-2[%d]",f_grp_id);
			//logmessage("resvInd = %s szRcsMsg = %s\n",resvInd,szRcsMsg);
			
			// 2022_0408   			
			//snprintf(szRcsMsg, sizeof(szRcsMsg), "%s", 	
			//	entryVector.at(atoi(resvInd)+4).c_str() );
			snprintf(szRcsMsg, sizeof(szRcsMsg), "%s", 	
				entryVector.at(nResvInd +  4).c_str() );

   			logmessage("[SHS]mappingfile getData start3-2[%d]",f_grp_id);
		} 
	}
    //sender key
    //if( ( atoi(resvInd)+5 < 0 ) || ( atoi(resvInd)+5 >= nCnt ) )
    //    return -1;    
    
    //logmessage("[SHS]mappingfile getData start4-2[%d]",f_grp_id);
    
    //20210408 위치변경
    memset(szSendKey,0x00,sizeof(szSendKey));
   	                  
    if(nCnt > 8)
    {
		// AlimTalk : SenderKey, NaverTalk : AuthCode
    	if(nChannel == 6 || nChannel == 7 || nChannel == 9 || 
			nChannel == 11)
    	{
			//snprintf(szSendKey, sizeof(szSendKey), "%s", 
    		//            entryVector.at(atoi(resvInd)+5).c_str() );
			snprintf(szSendKey, sizeof(szSendKey), "%s", 
    		            entryVector.at(nResvInd+5).c_str() );  
    	}
	}         
	
	//logmessage("[SHS]mappingfile getData start3-1[%d]",f_grp_id);             

 //   printf("mapping  start\n");
        // msg mapping    
	//20210408 위치변경
	memset(szButton,0x00,sizeof(szButton));
        	
	if(nCnt>8)
    {
		// AlimTalk, NaverTalk Button
    	if(nChannel == 6 || nChannel == 7 || nChannel == 9 || 
			nChannel == 11)
    	{	
			//snprintf(szButton, sizeof(szButton), "%s",
        	//	        entryVector.at(atoi(resvInd)+7).c_str() );
			snprintf(szButton, sizeof(szButton), "%s",
        		        entryVector.at(nResvInd+7).c_str() );     
    	}
	}    
	
	//logmessage("[SHS]mappingfile getData start4-1[%d]",f_grp_id);         
	
	//20210408 위치변경
	memset(szTitle,0x00,sizeof(szTitle));
   			
	if(nCnt>8)
    {
    	if(nChannel == 6 || nChannel == 7 || nChannel == 9)
    	{	
			//snprintf(szTitle, sizeof(szTitle), "%s",
        	//	        entryVector.at(atoi(resvInd)+8).c_str() );
			snprintf(szTitle, sizeof(szTitle), "%s",
        		        entryVector.at(nResvInd+8).c_str() );
    	}
	}  
	
	//logmessage("[SHS]mappingfile getData start5-1[%d]",f_grp_id);
	
	for(n=0;n<nCnt;n++)
    {
        memset(szFind,0x00,sizeof(szFind));
        sprintf(szFind,"<@%d@>",n);

        while( (idxMapping = msgTemplate.find(szFind) ) >= 0 ) {
            msgTemplate.replace( idxMapping,strlen(szFind), entryVector.at(n) );
        }
    }

//        printf("[%d][%d] nCnt , maxMappingCount\n",nCnt,maxMappingCount);

	// 추가 필드 삭제 
	for(n=nCnt;n<maxMappingCount;n++)
	{
    	memset(szFind,0x00,sizeof(szFind));
		sprintf(szFind,"<@%d@>",n);
//                printf("추가 삭제 [%s]\n",szFind);

		while( (idxMapping = msgTemplate.find(szFind) ) >= 0 )
			msgTemplate.replace( idxMapping,strlen(szFind), "" );
	}
        
    //logmessage("[SHS]mappingfile getData start2-0[%d]",f_grp_id);    
        
	// 파일의 특수기호를 \n 으로 변경한다
	memset(szFind,0x00,sizeof(szFind));
	strcpy(szFind,"^|^");
	while( (idxMapping = msgTemplate.find(szFind) ) >= 0 ) {
        msgTemplate.replace( idxMapping,strlen(szFind), "\n" );
	}
                   
    // 파일의 특수기호를 \n 으로 변경한다
	memset(szFind,0x00,sizeof(szFind));
	strcpy(szFind,"<BR/>");
	while( (idxMapping = msgTemplate.find(szFind) ) >= 0 ) {
        msgTemplate.replace( idxMapping,strlen(szFind), "\n" );               
	}

	//logmessage("[SHS]mappingfile getData start3-0[%d]",f_grp_id);   
	//20210408 위치변경
	memset(szReplaceMsg,0x00,sizeof(szReplaceMsg));
	memset(szMsg,0x00,sizeof(szMsg));
        		
	//SMS SEND MSG OR ALTALK/RCS/NaverTalk REPLACE MSG
	if(nChannel >= 0 && nChannel <= 5)
	{
		if((strcmp(szCallBack,"15884000") == 0 || 
			strcmp(szCallBack,"15889955") == 0)  && 
			strlen(szMbaseId) > 0)
		{			
			snprintf(szReplaceMsg, sizeof(szReplaceMsg), "%s", msgTemplate.c_str());
    	}
    	else
    	{    		
			snprintf(szMsg, sizeof(szMsg), "%s", msgTemplate.c_str());
    	}
    }
	
	// 11 : Navertalk
    else if(nChannel == 6 || nChannel == 7 || nChannel == 8 || nChannel == 9 || 
		nChannel == 11)
    {
      	//memcpy(szAtalkMsg,msgTemplate.c_str(),sizeof(szAtalkMsg)-1);
      	//memcpy(szReplaceMsg,msgTemplate.c_str(),sizeof(szReplaceMsg)-1);
		snprintf(szReplaceMsg, sizeof(szReplaceMsg), "%s", msgTemplate.c_str());
    }
    else
    {    	
		snprintf(szMsg, sizeof(szMsg), "%s", msgTemplate.c_str());
    }
    
    //logmessage("[SHS]mappingfile getData start4-0[%d]",f_grp_id);

    // Fallback MbaseId
    memset(szFallbackMbaseId, 0x00, sizeof(szFallbackMbaseId));
    if (nCnt > 12) {
        if (nChannel >= 0 && nChannel <= 5) {
            if (strcmp(szCallBack, "15884000") == 0 || 
                strcmp(szCallBack, "15889955") == 0) {
                snprintf(szFallbackMbaseId, sizeof(szFallbackMbaseId), "%s", 
                    entryVector.at(nResvInd+9).c_str());
            }
        }
        logmessage("[SHS]mappingfile getData szFallbackMbaseId[%s]",szFallbackMbaseId);
    }

    // Fallback RcsMsg
    memset(szFallbackRcsMsg, 0x00, sizeof(szFallbackRcsMsg));
    if (nCnt > 13) {
        if (nChannel >= 0 && nChannel <= 5) {
            if (strcmp(szCallBack, "15884000") == 0 || 
                strcmp(szCallBack, "15889955") == 0) {
                snprintf(szFallbackRcsMsg, sizeof(szFallbackRcsMsg), "%s", 
                    entryVector.at(nResvInd+10).c_str());
            }
        }
        logmessage("[SHS]mappingfile getData szFallbackRcsMsg[%s]",szFallbackRcsMsg);
    }
    
    return 1;
}

void CMappingFile::split(std::string& text, std::string& separators, std::vector<std::string>& words)
{
    int n = text.length();
    int start=0, stop;

    stop = text.find_first_of(separators) + 1;
    words.push_back(text.substr(start, stop - start-1));
    start = stop;

    while ((start > 0) && (start <= n)) {
        stop = text.find_first_of(separators,start) +1; 
        if ((stop <=0) || (stop > n)) {
            words.push_back(text.substr(start, stop - start));
            stop = n;
            break;
        }

        words.push_back(text.substr(start, stop - start-1));
        start = stop;
    }   
}



