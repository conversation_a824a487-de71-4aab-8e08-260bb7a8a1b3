#define  SQLCA_STORAGE_CLASS extern
#include <sqlca.h>
#include <iostream>
#include <string.h>
#include <queue>
#include "file_batch_struct.h"

EXEC SQL INCLUDE sendgrpfetchrec.h;

typedef std::queue<CGroupInfo> GroupInfoQueue;
EXEC SQL INCLUDE batch_db_v1.h;

#include "kscommon.h"


int CBatchDbV1::getDataGroup(
            sql_context db,
            struct sqlca sqlca,
            CGroupInfo& groupInfo,
            GroupInfoQueue& qGroup)
{
    memset(&sqlca,0x00,sizeof(sqlca));
    EXEC SQL BEGIN DECLARE SECTION;
        SendGrpFetchRec sendGrpFetchRec;
    EXEC SQL END DECLARE SECTION;

    if( !(qGroup.empty()) )
    {
        groupInfo = qGroup.front();
        qGroup.pop();
        return 1;
    }

    EXEC SQL CONTEXT USE :db;
    EXEC SQL DECLARE groupInfoCur CURSOR FOR

        SELECT A.F_GRP_ID  , <PERSON><PERSON>SND_SEQ ,  PTN_ID, PRIORITY, JOB_CODE, FLAG_PRO, MSG_CNT,MSG_TYPE,
               TELCO_INFO, TO_CHAR(REQ_DATE,'YYYYMMDDHH24MISS'), FILE_NAME, 
               A.WORK_CNT
                   FROM TBL_RESERV_F_GRP A, TBL_RESERV_F_GRP_SND B
                   WHERE 
                   A.FLAG_PRO in ( 0 , 4, 5 ) 
                   AND A.F_GRP_ID = B.F_GRP_ID
                   AND B.WORK_TYPE = 'R'
                   AND B.REQ_DATE <= SYSDATE
                   AND MSG_TYPE IN (0,1);

	/*
    printf("cur for [%d]\n",sqlca.sqlcode);
	*/
   
    if( sqlca.sqlcode < 0 )
        return sqlca.sqlcode;

    EXEC SQL CONTEXT USE :db;
    EXEC SQL OPEN groupInfoCur;

	/*
    printf("open [%d]\n",sqlca.sqlcode);
	*/
    if( sqlca.sqlcode < 0 )
        return sqlca.sqlcode;


    while(1) {

    EXEC SQL CONTEXT USE :db;
        EXEC SQL FETCH groupInfoCur INTO :sendGrpFetchRec;
/*
        printf("fetch [%d]\n",sqlca.sqlcode);
*/

        if( sqlca.sqlcode != 0 )
        { // no data
            EXEC SQL CONTEXT USE :db;
            EXEC SQL CLOSE groupInfoCur;
            EXEC SQL CONTEXT USE :db;
            EXEC SQL COMMIT WORK;

            return 0;
        }

        if( sqlca.sqlcode < 0 )
            return sqlca.sqlcode;

		//file_name initialize (20230515)
		memset(&groupInfo,0x00,sizeof(CGroupInfo));
		
        groupInfo.f_grp_id = sendGrpFetchRec.f_grp_id;
        groupInfo.snd_seq = sendGrpFetchRec.snd_seq;
        groupInfo.ptn_id = sendGrpFetchRec.ptn_id;
        groupInfo.priority = sendGrpFetchRec.priority;
        groupInfo.job_code = sendGrpFetchRec.job_code;
        groupInfo.flag_pro = sendGrpFetchRec.flag_pro;
        groupInfo.msg_cnt = sendGrpFetchRec.msg_cnt;
        groupInfo.msg_type = sendGrpFetchRec.msg_type;
        memcpy(groupInfo.telco_info,(char*)sendGrpFetchRec.telco_info.arr,sendGrpFetchRec.telco_info.len);
        memcpy(groupInfo.reg_date,(char*)sendGrpFetchRec.reg_date.arr,sendGrpFetchRec.reg_date.len);
        memcpy(groupInfo.file_name,(char*)sendGrpFetchRec.file_name.arr,sendGrpFetchRec.file_name.len);
        groupInfo.work_cnt = sendGrpFetchRec.work_cnt;

        qGroup.push(groupInfo);
        wait_a_moment(10000);

    }

    EXEC SQL CONTEXT USE :db;
    EXEC SQL CLOSE groupInfoCur;

    EXEC SQL CONTEXT USE :db;
    EXEC SQL COMMIT WORK;
    return 1;
}

int CBatchDbV1::upGroupStart(
                sql_context db,
                struct sqlca sqlca,
                CGroupInfo& groupInfo)
{
    memset(&sqlca,0x00,sizeof(sqlca));
    EXEC SQL BEGIN DECLARE SECTION;
    int f_grp_id;
    int work_cnt;
    int snd_seq;
    EXEC SQL END DECLARE SECTION;

    f_grp_id = groupInfo.f_grp_id;
    work_cnt = groupInfo.work_cnt;
    snd_seq = groupInfo.snd_seq;


    EXEC SQL CONTEXT USE :db;
    EXEC SQL UPDATE 
        TBL_RESERV_F_GRP 
        SET START_DATE = NVL(START_DATE,SYSDATE), FLAG_PRO=9
        WHERE 
        F_GRP_ID =: f_grp_id
        AND MSG_TYPE IN (0,1);

    if( sqlca.sqlcode != 0 ) return sqlca.sqlcode;

    EXEC SQL CONTEXT USE :db;
    EXEC SQL UPDATE
        TBL_RESERV_F_GRP_SND 
        SET START_DATE = SYSDATE , WORK_TYPE = 'S' , START_LINE =: work_cnt
        WHERE
        F_GRP_ID =: f_grp_id AND SND_SEQ =: snd_seq;

    if( sqlca.sqlcode != 0 ) return sqlca.sqlcode;


    EXEC SQL CONTEXT USE :db;
    EXEC SQL COMMIT;

    return sqlca.sqlcode;

}



