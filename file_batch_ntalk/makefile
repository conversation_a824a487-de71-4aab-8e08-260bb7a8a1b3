CC=g++
PROC=proc
RM=rm -rf
DBSTRING=NEO223
DBID=neosms2
DBPASS=password
COPY=cp

ORACLE_HOME = /usr/lib/oracle/21/client64

ORALIB = -lclntsh
ORALIB1 = $(ORACLE_HOME)/lib
ORALIB2 = $(OR<PERSON>LE_HOME)/plsql/lib
ORALIB3 = $(ORACLE_HOME)/network/lib
#ORA_INC = $(ORACLE_HOME)/precomp/public
ORA_INC = /usr/include/oracle/21/client64

INCLUDE =   -I$(ORA_INC)
LINKFLAGS = -L$(ORALIB1) -L$(ORALIB2) -L$(ORALIB3) -L$(ORA_INC)

KSLIBRARY_PATH=$(HOME)/library
KSLIBRARY_INC=$(HOME)/library

COMMAND_HOME = $(HOME)/project_vs/file_batch
#COMMAND_LIB = /user/neosms/command_file_ntalk/obj/sms_ctrlsub++.o
COMMAND_LIB = $(COMMAND_HOME)/command_file_ntalk/obj/sms_ctrlsub++.o
#COMMAND_INC = /user/neosms/command_file_ntalk/inc
COMMAND_INC = $(COMMAND_HOME)/command_file_ntalk/inc

#unix only
#CFLAGS = -lsocket -lnsl
#CFLAGS = -lrt
CFLAGS = -lrt -DDEBUG=5

#ORG_D=${HOME}/file_batch_key
#BIN_D=bin
BIN_D=bin_tmp
INST_D=bin
OBJ_D=obj
LIB_D=lib
INC_D=inc
SRC_D=src

all : file_batch

file_batch: $(OBJ_D)/file_batch.o $(OBJ_D)/command_util.o $(OBJ_D)/batch_db_v1.o $(OBJ_D)/mappingfile.o
	${CC} -g  $^ $(COMMAND_LIB) -I$(COMMAND_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lksconfig -lkscommon $(CFLAGS) ${LINKFLAGS} $(ORALIB) ${INCLUDE} -I${INC_D} -o $(BIN_D)/$@

$(OBJ_D)/file_batch.o: $(SRC_D)/file_batch.cpp
	$(RM) -rf $(OBJ_D)/file_batch.*
	$(COPY) $(SRC_D)/file_batch.cpp $(OBJ_D)/file_batch.pc
	$(PROC) iname=$(OBJ_D)/file_batch.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(COMMAND_INC)   cpp_suffix=cpp CODE=CPP  PARSE=PARTIAL THREADS=YES SQLCHECK=FULL userid=$(DBID)/$(DBPASS)@$(DBSTRING)
	$(CC)  -g -o $(OBJ_D)/file_batch.o  -I$(ORA_INC) -I$(KSLIBRARY_INC) -I${INC_D} -I$(COMMAND_INC) -c $(OBJ_D)/file_batch.cpp
	$(RM)  tp*


$(OBJ_D)/batch_db_v1.o: $(LIB_D)/batch_db_v1.cpp
	$(RM) -rf $(OBJ_D)/batch_db_v1.*
	$(COPY) $(LIB_D)/batch_db_v1.cpp $(OBJ_D)/batch_db_v1.pc
	$(PROC) iname=$(OBJ_D)/batch_db_v1.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(COMMAND_INC)  cpp_suffix=cpp CODE=CPP  PARSE=PARTIAL THREADS=YES SQLCHECK=FULL userid=$(DBID)/$(DBPASS)@$(DBSTRING)
	$(CC)  -g -o $(OBJ_D)/batch_db_v1.o  -I$(ORA_INC) -I$(KSLIBRARY_INC) -I${INC_D} -I$(COMMAND_INC) -c $(OBJ_D)/batch_db_v1.cpp
	$(RM)  tp*

$(OBJ_D)/mappingfile.o : $(LIB_D)/mappingfile.cpp
	$(CC) -g -o $@ -c $^ -I$(COMMAND_INC) -I$(INC_D)


$(OBJ_D)/command_util.o : $(LIB_D)/command_util.cpp
	$(CC) -g -o $@ -c $^ -I$(COMMAND_INC) -I$(INC_D)

clean:
	$(RM) $(OBJ_D)/file_batch.* $(OBJ_D)/command_util.* $(OBJ_D)/batch_db_v1.* $(OBJ_D)/mappingfile.*   tp*


install:
	mv $(BIN_D)/file_batch $(INST_D)/
