#ifndef _FILE_BATCH_H_
#define _FILE_BATCH_H_

#include <iostream>
#include <stdsms.h>
#include <command_util.h>
#include "ksconfig.h"
#include "kscommon.h"

#include <sqlca.h>



using namespace std;

/* config class */
class CConfigFileBatch {
    public:
        //char dbID[10];
        char dbID[20];
        //char dbPASS[10];
        char dbPASS[20];
        char dbSID[32];
        char filePath[64];
        int telcoDefault;
        int telcoCallbackDefault;
        int dbNoDataSleep;
        int maxMappingCount;
        int telcoRcs;
		int telcoNtk;
};

CConfigFileBatch gConf;

/* command gloval */
int activeProcess = TRUE;
struct _message_info    message_info;
struct _shm_info *shm_info;
char PROCESS_NO[ 7], PROCESS_NAME[36];

int configParse(char* file);


int Init_Oracle(sql_context ctx,struct sqlca sqlca);




#endif


