#ifndef _FILE_BATCH_MAPPING_H_
#define _FILE_BATCH_MAPPING_H_


#include <stdio.h>
#include <string>
#include <vector>

class CMappingFile {
public:
        CMappingFile();
        ~CMappingFile();
        int open(char* filename,int nWorkCnt,int maxCount);
        int close();
        int getData(
                char* dstInd, 
                char* callY , char* callInd ,
                char* resvY , char* resvInd ,
                std::string msgTemplate,
                std::string separators,
                int f_grp_id );

        void split(
                std::string& text, 
                std::string& separators,
                std::vector<std::string>& words);

        inline char* getDstAddr() { return szDstAddr; }
        inline char* getCallBack() { return szCallBack; }
        inline char* getMsg() { return szMsg; }
        inline char* getResv() { return szResv; }
        inline char* getCharge() { return szCharge; }
        inline char* getChannel() { return szChannel; }
        inline char* getTmplCd() { return szTmplCd; }
        inline char* getAtalkMsg() { return szAtalkMsg; }
        inline char* getRcsMsg() { return szRcsMsg; }
        inline char* getReplaceMsg() { return szReplaceMsg; }
        inline char* getReplaceTitle() { return szReplaceTitle; }
        inline char* getSendKey() { return szSendKey; }
        inline char* getButton() { return szButton; }
        inline char* getTitle() { return szTitle; }
        inline char* getMbaseId() { return szMbaseId; }

private:
        FILE* fp;
        char szDstAddr[16];
        char szCallBack[16];
        char szMsg[256+1];
        char szResv[200+1];
        char szCharge[1+1];
        char szChannel[2+1];
        char szTmplCd[10+1];
        char szMbaseId[40+1];
        char szAtalkMsg[2000+1];
        char szRcsMsg[2600+1];
        char szReplaceMsg[2000+1];
        char szReplaceTitle[200+1];
        char szSendKey[40+1];
        int maxMappingCount;
        char szButton[2000+1];
        //char szTitle[50+1];
        // title -> item highlight 변경
	char szTitle[200+1];
        char szFallbackMbaseId[40+1];
        char szFallbackRcsMsg[2600+1];
};


#endif




