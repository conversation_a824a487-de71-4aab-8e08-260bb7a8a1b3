#ifndef _COMMAND_UTIL_H_
#define _COMMAND_UTIL_H_

#define QUE_CHECK_TIME      20
#define MAXSIZE 2048

#include "stdsms.h"

#include <code_info.h>
#include <message_info.h>
#include <ml_ctrlsub.h>



extern struct _message_info message_info;
extern struct _shm_info *shm_info;
extern char   PROCESS_NO[ 7], PROCESS_NAME[36];
extern int    activeProcess;


void monitoring(const char* format,...);
void logmessage(const char* format,...);
void CloseProcess(int sig);
void Init_Server_Fork();
void sig_rtn(int sig);
#endif


