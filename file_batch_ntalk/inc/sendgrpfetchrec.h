#ifndef _SEND_GROUP_FETCH_RECORD_H_
#define _SEND_GROUP_FETCH_RECORD_H_

 EXEC SQL BEGIN DECLARE SECTION;
    typedef struct tag_sendgrpfetchrec {
        int f_grp_id;
        int snd_seq;
        int ptn_id;
        int priority;
        int job_code;
        int flag_pro;
        int msg_cnt;
        int msg_type;
        /*
        char telco_info[30+1];
        char reg_date[14+1];
        char file_name[512+1];
        */
        VARCHAR telco_info[30];
        VARCHAR reg_date[14];
        VARCHAR file_name[512];
        int work_cnt;
    } SendGrpFetchRec;
EXEC SQL END DECLARE SECTION;


#endif

