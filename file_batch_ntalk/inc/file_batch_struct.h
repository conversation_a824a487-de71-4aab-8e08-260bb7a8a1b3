#ifndef _FILE_BATCH_STRUCT_H_
#define _FILE_BATCH_STRUCT_H_

class CGroupInfo {
    public:
        int f_grp_id;
        int snd_seq;
        int ptn_id;
        int priority;
        int job_code;
        int flag_pro;
        int msg_cnt;
        int msg_type;
//        char process_name[50+1];
        char telco_info[30+1];
        char reg_date[14+1];
        char file_name[512+1];
        int work_cnt;
		char id_code[12+1];

};


class CSndGroupInfo {
    public:
        int nFlag,nPriority, nJobCode, nCntSec, nGetDataCnt , nWorkCnt, nMsgType;
        char szTelcoInfo[30+1];
        char szSendMsg[256+1];
        char szResvDataYn[1+1];
        char szResvData[12+1];

        char szDstInd[16+1];
        char szCallYn[1+1];
        char szCallInd[16+1];

        char szToken[4+1];

        int nFGrpId;
        int nTotal;
};




#endif


