#ifndef _BATCH_DB_V1_H_
#define _BATCH_DB_V1_H_

/* extern struct sqlca sqlca */
//#define  SQLCA_STORAGE_CLASS extern
//#include <sqlca.h>

#include <queue>
#include "file_batch_struct.h"

// #include "sendgrpfetchrec.h"
using namespace std;



// EXEC SQL INCLUDE sendgrpfetchrec.h;

/*
 * EXEC SQL BEGIN DECLARE SECTION;
    typedef struct tag_sendgrpfetchrec {
        int f_grp_id;
        int snd_seq;
        int ptn_id;
        int priority;
        int job_code;
        int flag_pro;
        int msg_cnt;
        char process_name[50+1];
        char telco_info[30+1];
        char reg_date[14+1];
        char file_name[512+1];
        int work_cnt;
    } SendGrpFetchRec;
EXEC SQL END DECLARE SECTION;
*/



class CBatchDbV1 {
    public:
        int getDataGroup(
                sql_context db,
                struct sqlca sqlca,
                CGroupInfo& groupInfo,
                GroupInfoQueue& qGroup);
        int upGroupStart(
                sql_context db,
                struct sqlca sqlca,
                CGroupInfo& groupInfo);

};

#endif


